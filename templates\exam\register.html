{% extends 'exam/base.html' %}

{% block title %}Register - KERNELiOS{% endblock %}

{% block content %}
<div class="register-container">
    <!-- Main Registration Terminal -->
    <div class="register-main">
        <div class="terminal-window terminal-xl">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">simulation_registration.py</div>
            </div>
            <div class="terminal-content">
                <div class="register-header">
                    <div class="register-logo">
                        <img src="/static/assets/logo.png" alt="KERNELiOS" style="width: 80px; height: 80px;">
                    </div>
                    <h1 class="register-title">Simulation Registration</h1>
                    <p class="register-subtitle">Register for the secure simulation system</p>
                </div>

                <div class="register-form-container">
                    <form method="post" class="register-form">
                        {% csrf_token %}

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">👤 Username</label>
                                <div class="input-container">
                                    <span class="input-prompt">></span>
                                    {{ form.username }}
                                </div>
                                {% if form.username.errors %}
                                    <div class="error-message">{{ form.username.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group">
                                <label class="form-label">📧 Email Address</label>
                                <div class="input-container">
                                    <span class="input-prompt">></span>
                                    {{ form.email }}
                                </div>
                                {% if form.email.errors %}
                                    <div class="error-message">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">🎯 Simulation Instance</label>
                            <div class="input-container">
                                <span class="input-prompt">></span>
                                {{ form.instance }}
                            </div>
                            {% if form.instance.errors %}
                                <div class="error-message">{{ form.instance.errors.0 }}</div>
                            {% endif %}
                            <div class="help-text">
                                Select your simulation instance. Each instance may have different scenario versions and schedules.
                            </div>

                            <!-- Instance Details Panel -->
                            <div id="instanceInfo" class="instance-info">
                                <div class="instance-header">
                                    <h4>📊 Instance Details</h4>
                                </div>
                                <div id="instanceDetails" class="instance-details"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">🔒 Password</label>
                                <div class="input-container">
                                    <span class="input-prompt">></span>
                                    {{ form.password1 }}
                                </div>
                                {% if form.password1.errors %}
                                    <div class="error-message">{{ form.password1.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group">
                                <label class="form-label">🔐 Confirm Password</label>
                                <div class="input-container">
                                    <span class="input-prompt">></span>
                                    {{ form.password2 }}
                                </div>
                                {% if form.password2.errors %}
                                    <div class="error-message">{{ form.password2.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-register">
                            <span>></span> Register for Simulation
                        </button>
                    </form>

                    <div class="register-footer">
                        <p>Already have an account? <a href="{% url 'login' %}" class="login-link">Login to exam</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Notice Terminal -->
    <div class="security-terminal">
        <div class="terminal-window terminal-md">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">security_notice.txt</div>
            </div>
            <div class="terminal-content">
                <div class="security-content">
                    <h3>🛡️ Security Notice</h3>
                    <div class="security-info">
                        <div class="terminal-line">🔐 Data Protection:</div>
                        <div class="terminal-line">├─ Military-grade encryption</div>
                        <div class="terminal-line">├─ Secure data transmission</div>
                        <div class="terminal-line">└─ Privacy compliance</div>
                        <div class="terminal-line"></div>
                        <div class="terminal-line">📊 Monitoring:</div>
                        <div class="terminal-line">├─ Exam integrity tracking</div>
                        <div class="terminal-line">├─ Security compliance</div>
                        <div class="terminal-line">└─ Activity logging</div>
                        <div class="terminal-line"></div>
                        <div class="terminal-line">✅ All systems secure</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Registration Stats Terminal -->
        <div class="terminal-window terminal-sm" style="margin-top: 1rem;">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">registration_stats.log</div>
            </div>
            <div class="terminal-content" id="registrationTerminal">
                <div class="terminal-line">📊 Registration Status:</div>
                <div class="terminal-line">├─ Active instances: <span style="color: var(--primary);">{{ form.instance.queryset.count }}</span></div>
                <div class="terminal-line">├─ Total students: <span style="color: var(--success);">1,247</span></div>
                <div class="terminal-line">├─ Success rate: <span style="color: var(--success);">94.2%</span></div>
                <div class="terminal-line">└─ System: <span style="color: var(--text-terminal);">ONLINE</span></div>
            </div>
        </div>
    </div>
</div>

<style>
.register-container {
    height: calc(100vh - 100px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    position: relative;
    overflow: hidden;
}

.register-main {
    max-width: 600px;
    width: 100%;
    z-index: 10;
}

.register-header {
    text-align: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--terminal-border);
}

.register-logo {
    margin-bottom: 0.5rem;
}

.register-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.register-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.register-form-container {
    padding: 0 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-prompt {
    color: var(--text-terminal-secondary);
    font-family: 'Fira Code', monospace;
    font-weight: 600;
    margin-right: 0.5rem;
    font-size: 1rem;
}

.form-input, select {
    flex: 1;
    margin-bottom: 0;
    background: var(--terminal-bg) !important;
    border: 2px solid var(--terminal-border) !important;
    color: var(--text-primary) !important;
    font-family: 'Fira Code', monospace !important;
    padding: 0.875rem 1rem !important;
    border-radius: 6px !important;
}

.form-input:focus, select:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1), var(--glow-primary) !important;
}

.error-message {
    color: var(--error);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    font-family: 'Fira Code', monospace;
}

.help-text {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-top: 0.5rem;
    font-style: italic;
}

.instance-info {
    margin-top: 1rem;
    background: var(--dark-card);
    border: 1px solid var(--terminal-border);
    border-radius: 8px;
    overflow: hidden;
    display: none;
}

.instance-header {
    background: var(--terminal-header);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--terminal-border);
}

.instance-header h4 {
    margin: 0;
    color: var(--primary);
    font-size: 0.875rem;
}

.instance-details {
    padding: 1rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-family: 'Fira Code', monospace;
}



.btn-register {
    width: 100%;
    font-family: 'Fira Code', monospace;
    font-size: 1rem;
    margin-bottom: 2rem;
}

.register-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--terminal-border);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.login-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
}

.login-link:hover {
    color: var(--primary-light);
}

.security-terminal {
    position: fixed;
    top: 15%;
    right: 5%;
    z-index: 1;
    opacity: 0.9;
    pointer-events: none;
    max-width: 300px;
}

.security-content h3 {
    color: var(--primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    text-align: center;
}

.security-info {
    font-family: 'Fira Code', monospace;
    font-size: 0.8rem;
}

.terminal-line {
    margin: 0.3rem 0;
    color: var(--text-terminal);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .security-terminal {
        max-width: 280px;
        right: 3%;
    }
}

@media (max-width: 768px) {
    .register-container {
        padding: 1rem;
    }

    .security-terminal {
        display: none;
    }

    .register-title {
        font-size: 1.75rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .security-notice {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .register-main {
        max-width: 500px;
    }
}

@media (max-width: 480px) {
    .register-main {
        max-width: 100%;
    }

    .register-form-container {
        padding: 0 0.5rem;
    }
}
</style>

<script>
// Instance data for JavaScript
const instanceData = {
    {% for instance in form.instance.queryset %}
    {{ instance.id }}: {
        name: "{{ instance.name|escapejs }}",
        description: "{{ instance.description|escapejs }}",
        version: "{{ instance.version.name|escapejs }}",
        studentCount: {{ instance.student_count }},
        registrationCloses: "{{ instance.registration_closes_at|date:'M d, Y H:i' }}"
    }{% if not forloop.last %},{% endif %}
    {% endfor %}
};

document.addEventListener('DOMContentLoaded', function() {
    // Animate security terminal
    setTimeout(() => {
        const lines = document.querySelectorAll('#registrationTerminal .terminal-line');
        lines.forEach((line, index) => {
            setTimeout(() => {
                line.style.opacity = '0';
                line.style.transform = 'translateX(-10px)';
                line.style.transition = 'all 0.5s ease';
                setTimeout(() => {
                    line.style.opacity = '1';
                    line.style.transform = 'translateX(0)';
                }, 50);
            }, index * 200);
        });
    }, 1000);

    // Instance selection handler
    const instanceSelect = document.querySelector('select[name="instance"]');
    const instanceInfo = document.getElementById('instanceInfo');
    const instanceDetails = document.getElementById('instanceDetails');

    if (instanceSelect && instanceInfo && instanceDetails) {
        instanceSelect.addEventListener('change', function() {
            const selectedId = this.value;

            if (selectedId && instanceData[selectedId]) {
                const instance = instanceData[selectedId];

                instanceDetails.innerHTML = `
                    <div style="display: grid; gap: 0.75rem;">
                        <div><span style="color: var(--primary);">Name:</span> ${instance.name}</div>
                        <div><span style="color: var(--primary);">Description:</span> ${instance.description || 'No description provided'}</div>
                        <div><span style="color: var(--primary);">Version:</span> ${instance.version}</div>
                        <div><span style="color: var(--primary);">Students:</span> ${instance.studentCount}</div>
                        <div><span style="color: var(--primary);">Closes:</span> ${instance.registrationCloses}</div>
                    </div>
                `;

                instanceInfo.style.display = 'block';
            } else {
                instanceInfo.style.display = 'none';
            }
        });
    }

    // Focus first input
    const firstInput = document.querySelector('input[name="username"]');
    if (firstInput) {
        setTimeout(() => firstInput.focus(), 500);
    }
});
</script>

{% endblock %}
